import time, os, sys, socket, ustruct, network
from media.sensor import Sensor
from media.media import MediaManager
import image
import gc

# 配置参数
WIFI_SSID = "K230_Stream"
WIFI_PASSWORD = "12345678"
ESP_IP = "***********"
ESP_PORT = 8888

# 图像参数
IMAGE_WIDTH = 640
IMAGE_HEIGHT = 480
JPEG_QUALITY = 50
TARGET_FPS = 30
MAX_RETRIES = 10
MAX_ERRORS = 20
HEARTBEAT_INTERVAL = 5000  # 5秒心跳

def connect_to_wifi(ssid, password):
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)

    if not wlan.isconnected():
        print(f"Connecting to {ssid}")
        wlan.connect(ssid, password)

        start_time = time.ticks_ms()
        while not wlan.isconnected():
            if time.ticks_ms() - start_time > 10000:
                return False
            time.sleep_ms(50)
    return True

def setup_camera():
    sensor = Sensor(id=2)
    sensor.reset()
    sensor.set_framesize(Sensor.VGA, chn=0)  # 320x240
    sensor.set_pixformat(Sensor.RGB565, chn=0)
    MediaManager.init()
    sensor.run()
    return sensor

def capture_and_compress(sensor):
    try:
        img = sensor.snapshot(chn=0)
        return img.compress(quality=JPEG_QUALITY, to_bytes=True)
    except:
        return None

def connect_to_esp():
    retry = 0
    while retry < MAX_RETRIES:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((ESP_IP, ESP_PORT))
            sock.settimeout(None)
            return sock
        except:
            retry += 1
            time.sleep(2)
    return None

def send_jpeg_data(sock, data):
    if not sock or not data:
        return False

    try:
        # 发送帧大小 (4字节小端序)
        sock.send(ustruct.pack("<I", len(data)))

        # 分块发送数据
        chunk_size = 1024
        total_sent = 0
        while total_sent < len(data):
            sent = sock.send(data[total_sent:total_sent+chunk_size])
            total_sent += sent
        return True
    except:
        return False

def main():
    print("K230 Video Streaming System")

    if not connect_to_wifi(WIFI_SSID, WIFI_PASSWORD):
        print("WiFi connection failed")
        return

    sensor = setup_camera()
    time.sleep(1)

    sock = connect_to_esp()
    if not sock:
        print("ESP connection failed")
        sensor.stop()
        MediaManager.deinit()
        return

    frame_count = 0
    last_time = time.ticks_ms()
    last_heartbeat = time.ticks_ms()

    try:
        while True:
            start_time = time.ticks_ms()

            # 心跳检测
            if time.ticks_ms() - last_heartbeat > HEARTBEAT_INTERVAL:
                try:
                    sock.send(b"HB")
                    last_heartbeat = time.ticks_ms()
                except:
                    print("Reconnecting...")
                    sock = connect_to_esp()
                    if not sock: break

            # 捕获和发送图像
            jpeg_data = capture_and_compress(sensor)
            if jpeg_data and send_jpeg_data(sock, jpeg_data):
                frame_count += 1

            # 显示帧率
            current_time = time.ticks_ms()
            if current_time - last_time >= 1000:
                fps = frame_count * 1000 / (current_time - last_time)
                print(f"FPS: {fps:.1f}, Size: {len(jpeg_data)/1024:.1f}KB")
                frame_count = 0
                last_time = current_time

            # 控制帧率
            elapsed = time.ticks_ms() - start_time
            delay = max(0, int(1000/TARGET_FPS - elapsed))
            time.sleep_ms(delay)

            gc.collect()

    except KeyboardInterrupt:
        pass
    finally:
        sock.close()
        sensor.stop()
        MediaManager.deinit()

if __name__ == "__main__":
    main()
