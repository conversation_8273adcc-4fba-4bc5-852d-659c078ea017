#include <ESP8266WiFi.h>
#include <ESPAsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <Arduino.h>

// STA网络配置
const char *sta_ssid = "Xiaomi_401";
const char *sta_password = "07712023495";
// AP网络配置
const char *ap_ssid = "K230_Stream";
const char *ap_password = "12345678";
// K230 TCP端口
const int K230_PORT = 8888;
// WebSocket端口
const int WEBSOCKET_PORT = 81;

// 创建TCP服务器和客户端
WiFiServer tcpServer(K230_PORT);
WiFiClient k230Client;
// 创建异步Web服务器和WebSocket
AsyncWebServer server(WEBSOCKET_PORT);
AsyncWebSocket ws("/ws");

// HTML页面
const char index_html[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>K230 Video Stream</title>
    <style>
        #videoContainer {
            width: 100%;
            max-width: 640px;
            margin: 0 auto;
        }
        #videoStream {
            width: 100%;
            background: #000;
        }
        .stats {
            text-align: center;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center;">K230 Live Stream</h1>
    <div id="videoContainer">
        <img id="videoStream" src="" alt="Live Stream">
    </div>
    <div class="stats">
        <span id="status">Status: Connecting...</span> | 
        <span id="fps">FPS: 0</span> |
        <span id="size">Size: 0 KB</span>
    </div>

    <script>
        const video = document.getElementById('videoStream');
        const status = document.getElementById('status');
        const fpsDisplay = document.getElementById('fps');
        const sizeDisplay = document.getElementById('size');
        
        const ws = new WebSocket('ws://' + window.location.hostname + ':81/ws');
        let frameCount = 0;
        let lastTime = Date.now();
        let lastSize = 0;
        
        ws.onopen = () => {
            status.textContent = 'Status: Connected';
        };
        
        ws.onmessage = (event) => {
            if (event.data instanceof Blob) {
                const url = URL.createObjectURL(event.data);
                video.src = url;
                lastSize = event.data.size;
                
                // 更新帧率
                frameCount++;
                const now = Date.now();
                if (now - lastTime >= 1000) {
                    fpsDisplay.textContent = `FPS: ${frameCount}`;
                    sizeDisplay.textContent = `Size: ${(lastSize/1024).toFixed(1)} KB`;
                    frameCount = 0;
                    lastTime = now;
                }
            }
        };
        
        ws.onclose = () => {
            status.textContent = 'Status: Disconnected';
        };
        
        ws.onerror = (error) => {
            status.textContent = 'Status: Error - ' + error.type;
        };
    </script>
</body>
</html>
)rawliteral";

void setup() {
  // 初始化串口通信
  Serial.begin(115200);
  
  // 设置WiFi为STA+AP模式
  WiFi.mode(WIFI_AP_STA);
  
  // 连接到STA网络
  WiFi.begin(sta_ssid, sta_password);
  for(int i = 0; i < 20 && WiFi.status() != WL_CONNECTED; i++) {
    delay(500);
    Serial.print(".");
  }
  
  // 设置AP网络
  WiFi.softAP(ap_ssid, ap_password);
  Serial.printf("AP IP: %s\n", WiFi.softAPIP().toString().c_str());
  
  // 启动TCP服务器
  tcpServer.begin();
  
  // 处理WebSocket事件
  ws.onEvent([](AsyncWebSocket *server, AsyncWebSocketClient *client, 
               AwsEventType type, void *arg, uint8_t *data, size_t len) {
    switch (type) {
      case WS_EVT_CONNECT:
        Serial.printf("WebSocket client #%u connected from %s\n", client->id(), client->remoteIP().toString().c_str());
        break;
      case WS_EVT_DISCONNECT:
        Serial.printf("WebSocket client #%u disconnected\n", client->id());
        break;
      case WS_EVT_ERROR:
        Serial.printf("WebSocket client #%u error(%u): %s\n", client->id(), *((uint16_t*)arg), (char*)data);
        break;
      case WS_EVT_PONG:
        Serial.printf("WebSocket client #%u pong[%u]: %s\n", client->id(), len, (len)?(char*)data:"");
        break;
      case WS_EVT_DATA:
        // 处理接收到的数据（如果需要）
        break;
    }
  });
  
  // 将WebSocket处理器添加到服务器
  server.addHandler(&ws);
  
  // 处理根路径请求，返回HTML页面
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request) {
    request->send_P(200, "text/html", index_html);
  });
  
  // 启动Web服务器
  server.begin();
}

void loop() {
  // 检查K230客户端连接
  if (!k230Client.connected()) {
    k230Client = tcpServer.accept();
    if (k230Client) {
      Serial.println("K230 connected");
      k230Client.setNoDelay(true);
    }
  }
  
  // 处理视频数据
  if (k230Client.connected() && k230Client.available() >= 4) {
    // 读取帧大小
    uint8_t sizeBuf[4];
    k230Client.readBytes(sizeBuf, 4);
    uint32_t frameSize = *((uint32_t*)sizeBuf);
    
    // 验证帧大小
    if(frameSize > 0 && frameSize < 50000) {
      // 等待完整帧
      unsigned long start = millis();
      while(k230Client.available() < (int)frameSize && millis() - start < 5000) {
        delay(1);
      }
      
      if(k230Client.available() >= (int)frameSize) {
        // 读取并转发
        uint8_t *buffer = new uint8_t[frameSize];
        k230Client.readBytes(buffer, frameSize);
        ws.binaryAll(buffer, frameSize);
        delete[] buffer;
      }
    }
  }
  
  // 清理断开的WebSocket客户端
  ws.cleanupClients();
  delay(1);
}    