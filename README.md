# ESP8266 + K230 实时视频监控系统

## 系统概述
本系统通过ESP8266创建WebSocket服务器，接收K230发送的JPEG视频流，并在网页上实时显示。

## 硬件要求
- ESP8266开发板
- K230开发板
- 路由器（可选，用于外网访问）

## 网络架构
```
K230 ──WiFi──> ESP8266(AP) ──WiFi──> 路由器 ──> 用户设备
     (客户端)    (热点+客户端)   (网关)    (浏览器)
```

## 配置说明

### 1. ESP8266配置
编辑 `config.h` 文件：
```cpp
#define STA_SSID "your_router_ssid"        // 修改为您的路由器SSID
#define STA_PASSWORD "your_router_password" // 修改为您的路由器密码
```

### 2. K230配置
K230程序中的配置已正确设置：
- WiFi SSID: "K230_Stream"
- WiFi密码: "12345678"
- ESP IP: ***********
- TCP端口: 8888

## 使用步骤

### 1. 烧录ESP8266程序
1. 在Arduino IDE中打开 `esp8266_websocket_server.ino`
2. 修改 `config.h` 中的路由器信息
3. 选择正确的ESP8266开发板
4. 编译并上传程序

### 2. 运行K230程序
1. 将 `untitled_4.py` 上传到K230
2. 运行程序：`python untitled_4.py`

### 3. 访问监控页面
- 通过ESP8266 IP访问：`http://***********`
- 通过路由器IP访问：`http://[ESP8266的STA_IP]`

## 通信协议

### K230 -> ESP8266 (TCP Socket)
1. **帧数据格式**：
   - 4字节帧大小（小端序）
   - JPEG数据（分块发送，每块1024字节）

2. **心跳包**：
   - 每5秒发送 "HB" 字符串
   - ESP8266超时时间：10秒

### ESP8266 -> 浏览器 (WebSocket)
- 直接转发JPEG二进制数据
- 浏览器接收后创建Blob URL显示

## 性能参数
- 目标帧率：30 FPS
- 图像分辨率：640x480
- JPEG质量：50%
- 最大帧大小：64KB
- 缓冲区大小：64KB

## 故障排除

### 1. K230连接失败
- 检查ESP8266 AP是否正常启动
- 确认WiFi密码正确
- 查看串口输出的连接状态

### 2. 网页无法访问
- 确认ESP8266已连接到路由器
- 检查防火墙设置
- 尝试直接访问AP IP：***********

### 3. 视频流中断
- 检查K230心跳状态
- 确认网络稳定性
- 查看ESP8266内存使用情况

### 4. 帧率过低
- 降低JPEG质量参数
- 减小图像分辨率
- 检查网络带宽

## 技术特性
- **双模WiFi**：ESP8266同时作为AP和STA
- **实时传输**：WebSocket低延迟传输
- **自动重连**：网络断开自动恢复
- **内存优化**：64KB循环缓冲区
- **错误处理**：完善的异常处理机制

## 扩展功能
- 支持多客户端同时观看
- 可添加录像功能
- 支持双向音频传输
- 可集成运动检测

## 注意事项
1. ESP8266内存有限，建议控制同时连接的客户端数量
2. 长时间运行建议添加看门狗重启机制
3. 生产环境建议加密传输和身份验证
4. 注意功耗管理，特别是电池供电场景
